"""
Analyze the simulation to understand why we're getting perfect performance
"""

import requests
import json

def analyze_simulation():
    print("=== Simulation State Analysis ===")
    
    # Test if simulation provides realistic scenarios
    try:
        response = requests.get('http://localhost:5000/api/simulation/state')
        state = response.json()
        
        print(f"Vessels: {len(state.get('vessels', []))}")
        print(f"Current delay rate: {state.get('delayRate', 'N/A')}%")
        print(f"Current time: {state.get('currentTime', 'N/A')}")
        
        # Check vessel details
        vessels = state.get('vessels', [])
        if vessels:
            vessel = vessels[0]
            print(f"Sample vessel: ID={vessel.get('id', 'N/A')}, "
                  f"Containers={vessel.get('containerCount', 'N/A')}, "
                  f"Delayed={vessel.get('isDelayed', 'N/A')}")
        else:
            print("No vessels in simulation!")
            
        # Check berths
        berths = state.get('berths', [])
        print(f"Berths: {len(berths)}")
        if berths:
            occupied_berths = sum(1 for b in berths if b.get('isOccupied', False))
            print(f"Occupied berths: {occupied_berths}/{len(berths)}")
            
    except Exception as e:
        print(f"Error getting state: {e}")
        return
    
    print("\n=== Testing Scenario Variability ===")
    # Test multiple resets to see if scenarios vary
    delay_rates = []
    vessel_counts = []
    
    for i in range(5):
        try:
            reset_response = requests.post('http://localhost:5000/api/simulation/reset')
            reset_state = reset_response.json()
            
            vessels_count = len(reset_state.get('vessels', []))
            delay_rate = reset_state.get('delayRate', 0)
            
            vessel_counts.append(vessels_count)
            delay_rates.append(delay_rate)
            
            print(f"Reset {i+1}: Vessels={vessels_count}, Delay={delay_rate}%")
            
        except Exception as e:
            print(f"Reset {i+1} failed: {e}")
    
    print(f"\nVariability analysis:")
    print(f"Vessel count range: {min(vessel_counts)} - {max(vessel_counts)}")
    print(f"Delay rate range: {min(delay_rates)} - {max(delay_rates)}")
    
    if len(set(vessel_counts)) == 1 and len(set(delay_rates)) == 1:
        print("⚠️  WARNING: No variability detected - simulation might be too deterministic!")
    
    print("\n=== Testing Action Impact ===")
    # Test if different actions produce different results
    
    # Reset simulation
    requests.post('http://localhost:5000/api/simulation/reset')
    
    # Try different actions and see if they produce different outcomes
    actions = [
        {"berthAllocation": 0, "agvAssignment": 0, "yardBlockSelection": 0, 
         "priorityWeights": [0.5, 0.3, 0.2], "resourceAllocationBias": 0.5, "congestionAvoidance": 0.5},
        {"berthAllocation": 3, "agvAssignment": 23, "yardBlockSelection": 15, 
         "priorityWeights": [0.2, 0.3, 0.5], "resourceAllocationBias": 0.9, "congestionAvoidance": 0.1},
    ]
    
    results = []
    for i, action in enumerate(actions):
        try:
            # Reset before each test
            requests.post('http://localhost:5000/api/simulation/reset')
            
            # Take action
            response = requests.post('http://localhost:5000/api/simulation/step', 
                                   json=action, 
                                   headers={'Content-Type': 'application/json'})
            result = response.json()
            
            reward = result.get('reward', 0)
            new_state = result.get('newState', {})
            delay_rate = new_state.get('delayRate', 0)
            
            results.append((reward, delay_rate))
            print(f"Action {i+1}: Reward={reward}, Delay={delay_rate}%")
            
        except Exception as e:
            print(f"Action {i+1} failed: {e}")
    
    if len(set(results)) == 1:
        print("⚠️  WARNING: Different actions produce identical results!")
        print("This suggests actions may not be having meaningful impact.")
    else:
        print("✅ Different actions produce different results.")

if __name__ == "__main__":
    analyze_simulation()
