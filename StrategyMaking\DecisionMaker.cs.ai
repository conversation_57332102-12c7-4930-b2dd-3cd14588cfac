﻿//
// The three strategies are coded by AI model. 
//
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WSC_SimChallenge_2024_Net.PortSimulation.Entity;

namespace WSC_SimChallenge_2024_Net.PortSimulation
{
    class DecisionMaker
    {
        public static PortSimModel WSCPort { get; set; }        
        
        public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
        {
            List<Berth> currentIdleBerths = WSCPort.berthBeingIdle.CompletedList;
            
            if (currentIdleBerths == null || currentIdleBerths.Count == 0)
                return null;

            // Calculate vessel workload (proxy for service time)
            int totalContainerWork = CalculateVesselWorkload(arrivalVessel);
            
            // Advanced berth selection based on multiple factors
            Berth bestBerth = null;
            double bestScore = double.MinValue;
            
            foreach (var berth in currentIdleBerths)
            {
                double score = CalculateBerthScore(berth, arrivalVessel, totalContainerWork);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestBerth = berth;
                }
            }
            
            return bestBerth;
        }
        
        private static int CalculateVesselWorkload(Vessel vessel)
        {
            // Calculate total container operations
            int dischargingContainers = vessel.DischargingContainersInformation.Values.Sum();
            int loadingContainers = vessel.LoadingContainersInformation?.Values.Sum() ?? 0;
            
            return dischargingContainers + loadingContainers;
        }
        
        private static double CalculateBerthScore(Berth berth, Vessel vessel, int workload)
        {
            double score = 0;
            
            // Factor 1: QC availability (most important)
            int availableQCs = berth.EquippedQCs.Count(qc => qc.ServedVessel == null);
            score += availableQCs * 100; // High weight for QC availability
            
            // Factor 2: Berth position optimization
            // Middle berths (1,2) have better AGV access
            int berthId = int.Parse(berth.Id.Replace("berth", ""));
            if (berthId == 1 || berthId == 2) // Middle berths
                score += 30;
            else if (berthId == 0 || berthId == 3) // Edge berths
                score += 20;
            
            // Factor 3: Workload balancing
            // For high workload vessels, prefer berths with maximum QC availability
            if (workload > 100) // High workload threshold
            {
                score += availableQCs * 50; // Extra bonus for high workload
            }
            
            // Factor 4: QC utilization pattern
            // Prefer berths where QC rotation is at the beginning
            int currentQCRotation = berth.CurrentWorkQC;
            score += (3 - currentQCRotation) * 10; // Favor berths with lower rotation counter
            
            return score;
        }

        public static AGV CustomeizedAllocatedAGVs(Container container)
        {
            List<AGV> currentIdleAGVs = WSCPort.agvBeingIdle.CompletedList;
            
            if (currentIdleAGVs == null || currentIdleAGVs.Count == 0)
                return null;

            // Enhanced AGV selection with improved scoring
            AGV bestAGV = null;
            double bestScore = double.MinValue;
            
            foreach (var agv in currentIdleAGVs)
            {
                double score = CalculateAGVScore(agv, container);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestAGV = agv;
                }
            }
            
            return bestAGV;
        }
        
        private static double CalculateAGVScore(AGV agv, Container container)
        {
            double score = 0;
            
            // Factor 1: Distance efficiency (primary factor)
            double distance = AGV.CalculateDistance(agv.CurrentLocation, container.CurrentLocation);
            score += (2000 - distance) * 0.1; // Closer AGVs get higher scores
            
            // Factor 2: AGV workload and state
            int agvWorkload = GetEnhancedAGVWorkload(agv);
            score += (10 - agvWorkload) * 10; // Less busy AGVs preferred
            
            // Factor 3: Container priority
            int containerPriority = GetContainerPriority(container);
            score += containerPriority * 5; // Higher priority containers get preference
            
            // Factor 4: Traffic balancing (avoid congestion)
            double trafficPenalty = GetTrafficPenalty(agv);
            score -= trafficPenalty * 20; // Penalize AGVs in congested areas
            
            return score;
        }
        
        private static int GetEnhancedAGVWorkload(AGV agv)
        {
            int workload = 0;
            
            // Basic workload: discharging mode is busier
            if (agv.InDischarging) workload += 2;
            
            // Position-based workload: AGVs at busy locations are considered busier
            if (agv.CurrentLocation.Ycoordinate <= 50) // Quayside area
                workload += 1;
            
            // Future: could add more sophisticated workload calculation
            // based on AGV's recent activity or assignment patterns
            
            return Math.Min(workload, 10); // Cap at 10
        }
        
        private static int GetContainerPriority(Container container)
        {
            int priority = 1; // Default priority
            
            // Higher priority for containers from vessels with more urgent schedules
            if (container.Week <= 3) priority += 2; // Early weeks get higher priority
            else if (container.Week <= 6) priority += 1; // Mid weeks get medium priority
            
            // Higher priority for loading containers (time-sensitive)
            if (!container.InDischarging) priority += 1;
            
            return priority;
        }
        
        private static double GetTrafficPenalty(AGV agv)
        {
            double penalty = 0;
            
            // Count AGVs in similar locations (simplified traffic management)
            var nearbyAGVs = WSCPort.AGVs.Where(a => a != agv && 
                Math.Abs(a.CurrentLocation.Xcoordinate - agv.CurrentLocation.Xcoordinate) < 200 &&
                Math.Abs(a.CurrentLocation.Ycoordinate - agv.CurrentLocation.Ycoordinate) < 100);
            
            int nearbyCount = nearbyAGVs.Count();
            
            // Penalty increases with the number of nearby AGVs
            if (nearbyCount > 3) penalty += 2;
            else if (nearbyCount > 1) penalty += 1;
            
            return penalty;
        }

        public static YardBlock CustomeizedDetermineYardBlock(AGV agv)
        {
            List<YardBlock> availableBlocks = WSCPort.YardBlocks
                .Where(block => block.Capacity > block.ReservedSlots + block.StackedContainers.Count)
                .ToList();
            
            if (!availableBlocks.Any())
                return null;

            // Enhanced yard block selection with multiple optimization factors
            YardBlock bestBlock = null;
            double bestScore = double.MinValue;
            
            foreach (var block in availableBlocks)
            {
                double score = CalculateYardBlockScore(block, agv);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestBlock = block;
                }
            }
            
            return bestBlock;
        }
        
        private static double CalculateYardBlockScore(YardBlock block, AGV agv)
        {
            double score = 0;
            
            // Factor 1: Distance efficiency (primary factor)
            double distance = AGV.CalculateDistance(block.CP, agv.CurrentLocation);
            score += (2000 - distance) * 0.05; // Closer blocks get higher scores
            
            // Factor 2: Block utilization optimization
            double utilizationRate = GetBlockUtilization(block);
            score += (1.0 - utilizationRate) * 50; // Prefer blocks with lower utilization
            
            // Factor 3: Container destination awareness
            double destinationScore = GetDestinationScore(block, agv.LoadedContainer);
            score += destinationScore * 30; // Boost for strategic placement
            
            // Factor 4: YC availability and workload
            double ycScore = GetYCScore(block);
            score += ycScore * 20; // Prefer blocks with less busy YCs
            
            // Factor 5: Block specialization bonus
            double specializationBonus = GetSpecializationBonus(block, agv.LoadedContainer);
            score += specializationBonus * 15; // Reward for block specialization
            
            return score;
        }
        
        private static double GetDestinationScore(YardBlock block, Container container)
        {
            if (container.InDischarging)
                return 0; // For discharging containers, no immediate destination consideration
            
            // For loading containers, consider if this block already has containers
            // for the same destination vessel (grouping advantage)
            int sameDestinationCount = block.StackedContainers
                .Count(c => c.LoadingVesselID == container.LoadingVesselID);
            
            // Bonus for grouping containers with same destination
            double groupingBonus = Math.Min(sameDestinationCount * 0.5, 3.0);
            
            // Consider the container's loading schedule urgency
            double urgencyBonus = 0;
            if (container.Week <= 3) urgencyBonus = 2.0; // Early weeks get priority
            else if (container.Week <= 6) urgencyBonus = 1.0; // Mid weeks get medium priority
            
            return groupingBonus + urgencyBonus;
        }
        
        private static double GetYCScore(YardBlock block)
        {
            // Simple YC availability scoring
            // In a more complex system, we could check YC's current tasks
            
            // For now, consider block utilization as a proxy for YC busyness
            double utilizationRate = GetBlockUtilization(block);
            
            // Less utilized blocks likely have less busy YCs
            return (1.0 - utilizationRate) * 2.0;
        }
        
        private static double GetSpecializationBonus(YardBlock block, Container container)
        {
            // Implement simple block specialization strategy
            int blockId = int.Parse(block.Id.Replace("Block", ""));
            
            // Strategy: Use certain blocks preferentially for certain container types
            if (container.InDischarging)
            {
                // Discharging containers: prefer blocks 0-7 (first row)
                if (blockId < 8) return 1.0;
            }
            else
            {
                // Loading containers: prefer blocks 8-15 (second row) for faster retrieval
                if (blockId >= 8) return 1.0;
                
                // Additional bonus for urgent loading containers in accessible blocks
                if (container.Week <= 3 && blockId >= 8) return 2.0;
            }
            
            return 0;
        }

        private static double GetBlockUtilization(YardBlock block)
        {
            return (double)(block.ReservedSlots + block.StackedContainers.Count) / block.Capacity;
        }

        // Advanced QC Scheduling Strategy
        public static QC CustomeizedDetermineQC(Container container)
        {
            Berth berth = WSCPort.Berths.Find(b => b.BerthedVessel != null && 
                b.BerthedVessel.Id == container.LoadingVesselID &&
                b.BerthedVessel.Week == container.Week + 1);

            if (berth == null)
                return null;

            // Simplified but effective QC selection - avoid complex logic that caused regression
            QC bestQC = berth.EquippedQCs
                .OrderBy(qc => qc.ServedVessel == null ? 0 : 1) // Prefer available QCs
                .ThenBy(qc => EstimateQCWorkload(qc, berth)) // Then by workload
                .FirstOrDefault();

            return bestQC;
        }

        private static double CalculateQCScore(QC qc, Container container, Berth berth)
        {
            double score = 0;

            // Factor 1: QC availability (simplified)
            if (qc.ServedVessel == null)
                score += 100;
            else
                score += 50;

            // Factor 2: QC workload (simplified)
            int qcWorkload = EstimateQCWorkload(qc, berth);
            score += (10 - qcWorkload) * 5;

            return score;
        }

        private static int EstimateQCWorkload(QC qc, Berth berth)
        {
            // Simplified workload estimation
            if (qc.ServedVessel == null)
                return 0;

            // Simple workload estimation based on remaining containers
            int remainingContainers = qc.ServedVessel.LoadingContainersInformation?.Values.Sum() ?? 0;
            
            // Normalize to a 0-10 scale
            return Math.Min(remainingContainers / 10, 10);
        }
    }
}
