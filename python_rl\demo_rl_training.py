"""
Demo script for WSC 2024 Port Simulation RL Training
Shows basic integration between Python RL and .NET simulation
"""

import os
import sys
import time
import numpy as np
import requests
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.monitor import Monitor

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from port_simulation_env import PortSimulationEnv


def test_basic_integration():
    """Test basic integration between Python and .NET"""
    print("🔍 Testing Basic RL Integration")
    print("=" * 50)
    
    # Test API connection
    print("\n1️⃣  Testing API connection...")
    try:
        response = requests.get("http://localhost:5000/api/simulation/state", timeout=5)
        if response.status_code == 200:
            print("✅ API connection successful")
        else:
            print(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        print("💡 Make sure to start the .NET simulation with: dotnet run -- --api")
        return False
    
    # Test environment creation
    print("\n2️⃣  Testing environment creation...")
    try:
        env = PortSimulationEnv()
        print("✅ Environment created successfully")
    except Exception as e:
        print(f"❌ Environment creation failed: {e}")
        return False
    
    # Test environment reset
    print("\n3️⃣  Testing environment reset...")
    try:
        obs, info = env.reset()
        print(f"✅ Environment reset successful")
        print(f"   Observation shape: {obs.shape}")
        print(f"   Info: {info}")
    except Exception as e:
        print(f"❌ Environment reset failed: {e}")
        return False
    
    # Test environment step
    print("\n4️⃣  Testing environment step...")
    try:
        # Create a random action
        action = env.action_space.sample()
        obs, reward, done, truncated, info = env.step(action)
        
        print(f"✅ Environment step successful")
        print(f"   Action: {action}")
        print(f"   Reward: {reward:.2f}")
        print(f"   Done: {done}")
        print(f"   Info: {info}")
    except Exception as e:
        print(f"❌ Environment step failed: {e}")
        return False
    
    # Test multiple steps
    print("\n5️⃣  Testing multiple steps...")
    try:
        total_reward = 0
        for i in range(5):
            action = env.action_space.sample()
            obs, reward, done, truncated, info = env.step(action)
            total_reward += reward
            
            delay_rate = info.get('delay_rate', 0)
            current_time = info.get('current_time', 0)
            
            print(f"   Step {i+1}: Time={current_time:.1f}h, Reward={reward:.2f}, Delay={delay_rate:.2f}%")
            
            if done or truncated:
                print(f"   Episode finished at step {i+1}")
                break
        
        print(f"✅ Multiple steps successful")
        print(f"   Total reward: {total_reward:.2f}")
    except Exception as e:
        print(f"❌ Multiple steps failed: {e}")
        return False
    
    env.close()
    print("\n🎉 All basic integration tests passed!")
    return True


def demo_rl_training():
    """Demonstrate basic RL training"""
    print("\n🤖 Demo RL Training")
    print("=" * 50)
    
    # Create environment
    print("\n1️⃣  Creating training environment...")
    try:
        env = PortSimulationEnv()
        env = Monitor(env)
        print("✅ Training environment created")
    except Exception as e:
        print(f"❌ Training environment creation failed: {e}")
        return False
    
    # Create PPO model
    print("\n2️⃣  Creating PPO model...")
    try:
        model = PPO(
            "MlpPolicy",
            env,
            verbose=1,
            learning_rate=3e-4,
            n_steps=64,  # Small for demo
            batch_size=32,
            n_epochs=3,
            gamma=0.99,
            device="auto"
        )
        print("✅ PPO model created")
        print(f"   Policy: {model.policy}")
        print(f"   Action space: {env.action_space}")
        print(f"   Observation space: {env.observation_space}")
    except Exception as e:
        print(f"❌ PPO model creation failed: {e}")
        return False
    
    # Short training demo
    print("\n3️⃣  Running short training demo...")
    try:
        print("   Training for 500 timesteps...")
        start_time = time.time()
        
        model.learn(
            total_timesteps=500,
            progress_bar=True
        )
        
        training_time = time.time() - start_time
        print(f"✅ Training completed in {training_time:.2f} seconds")
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return False
    
    # Test trained model
    print("\n4️⃣  Testing trained model...")
    try:
        obs, info = env.reset()
        total_reward = 0
        steps = 0
        
        print("   Running trained model for 10 steps...")
        for step in range(10):
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, done, truncated, info = env.step(action)
            
            total_reward += reward
            steps += 1
            
            delay_rate = info.get('delay_rate', 0)
            current_time = info.get('current_time', 0)
            
            print(f"   Step {step+1}: Time={current_time:.1f}h, Reward={reward:.2f}, Delay={delay_rate:.2f}%")
            
            if done or truncated:
                break
        
        print(f"✅ Model testing completed")
        print(f"   Total reward: {total_reward:.2f}")
        print(f"   Average reward: {total_reward/steps:.2f}")
    except Exception as e:
        print(f"❌ Model testing failed: {e}")
        return False
    
    env.close()
    print("\n🎉 RL training demo completed successfully!")
    return True


def main():
    """Main demo function"""
    print("🚀 WSC 2024 Port Simulation RL Integration Demo")
    print("=" * 60)
    
    # Test basic integration
    if not test_basic_integration():
        print("\n❌ Basic integration tests failed. Please check the setup.")
        return
    
    # Demo RL training
    if not demo_rl_training():
        print("\n❌ RL training demo failed. Please check the setup.")
        return
    
    print("\n" + "=" * 60)
    print("🎉 All demos completed successfully!")
    print("🚀 The WSC 2024 RL integration is working!")
    print("\n📈 Next steps:")
    print("   1. Run full training: python python_rl/train_rl_agent.py")
    print("   2. Adjust hyperparameters for better performance")
    print("   3. Implement more sophisticated reward functions")
    print("   4. Add more complex action spaces")
    print("   5. Experiment with different RL algorithms")


if __name__ == "__main__":
    main()
