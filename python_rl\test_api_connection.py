"""
Test script to verify API connection between Python RL environment and .NET simulation
"""

import requests
import json
import time
import sys


def test_api_connection(base_url="http://localhost:5000"):
    """Test the API connection and basic functionality"""
    
    print("🔍 Testing WSC 2024 Port Simulation API Connection")
    print(f"📡 Base URL: {base_url}")
    print("=" * 60)
    
    session = requests.Session()
    
    # Test 1: Basic connectivity
    print("\n1️⃣  Testing basic connectivity...")
    try:
        response = session.get(f"{base_url}/api/simulation/state", timeout=5)
        if response.status_code == 200:
            print("✅ API is reachable")
        else:
            print(f"⚠️  API returned status {response.status_code}")
            print(f"Response: {response.text}")
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is the .NET simulation running?")
        print("💡 Start the simulation with: dotnet run -- --api")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    # Test 2: Reset simulation
    print("\n2️⃣  Testing simulation reset...")
    try:
        response = session.post(f"{base_url}/api/simulation/reset", timeout=10)
        response.raise_for_status()
        
        state_data = response.json()
        print("✅ Reset successful")
        print(f"   Current time: {state_data.get('currentTime', 'N/A')}")
        print(f"   Vessels: {len(state_data.get('vessels', []))}")
        print(f"   Berths: {len(state_data.get('berths', []))}")
        print(f"   AGVs: {len(state_data.get('agvs', []))}")
        print(f"   QCs: {len(state_data.get('qcs', []))}")
        print(f"   Yard Blocks: {len(state_data.get('yardBlocks', []))}")
        print(f"   Delay Rate: {state_data.get('delayRate', 'N/A')}%")
        
    except Exception as e:
        print(f"❌ Reset failed: {e}")
        return False
    
    # Test 3: Get current state
    print("\n3️⃣  Testing state retrieval...")
    try:
        response = session.get(f"{base_url}/api/simulation/state", timeout=5)
        response.raise_for_status()
        
        state_data = response.json()
        print("✅ State retrieval successful")
        
        # Show sample vessel data
        vessels = state_data.get('vessels', [])
        if vessels:
            vessel = vessels[0]
            print(f"   Sample vessel: ID={vessel.get('id', 'N/A')}, "
                  f"Containers={vessel.get('containerCount', 'N/A')}, "
                  f"Delayed={vessel.get('isDelayed', 'N/A')}")
        
    except Exception as e:
        print(f"❌ State retrieval failed: {e}")
        return False
    
    # Test 4: Execute simulation step
    print("\n4️⃣  Testing simulation step...")
    try:
        # Create a sample action
        action = {
            "berthAllocation": 0,
            "agvAssignment": 5,
            "yardBlockSelection": 2,
            "priorityWeights": [0.4, 0.3, 0.3],
            "resourceAllocationBias": 0.5,
            "congestionAvoidance": 0.6
        }
        
        response = session.post(
            f"{base_url}/api/simulation/step",
            json=action,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        response.raise_for_status()
        
        step_result = response.json()
        print("✅ Simulation step successful")
        print(f"   Reward: {step_result.get('reward', 'N/A')}")
        print(f"   Done: {step_result.get('done', 'N/A')}")
        
        new_state = step_result.get('newState', {})
        print(f"   New time: {new_state.get('currentTime', 'N/A')}")
        print(f"   New delay rate: {new_state.get('delayRate', 'N/A')}%")
        
    except Exception as e:
        print(f"❌ Simulation step failed: {e}")
        return False
    
    # Test 5: Get performance metrics
    print("\n5️⃣  Testing performance metrics...")
    try:
        response = session.get(f"{base_url}/api/simulation/metrics", timeout=5)
        response.raise_for_status()
        
        metrics = response.json()
        print("✅ Metrics retrieval successful")
        print(f"   Vessel Delay Rate: {metrics.get('vesselDelayRate', 'N/A')}%")
        print(f"   Total Vessels: {metrics.get('totalVessels', 'N/A')}")
        print(f"   Delayed Vessels: {metrics.get('totalDelayedVessels', 'N/A')}")
        print(f"   Average Waiting Time: {metrics.get('averageWaitingTime', 'N/A')}")
        print(f"   Resource Utilization: {metrics.get('resourceUtilization', 'N/A')}%")
        
    except Exception as e:
        print(f"❌ Metrics retrieval failed: {e}")
        return False
    
    # Test 6: Multiple steps simulation
    print("\n6️⃣  Testing multiple simulation steps...")
    try:
        initial_time = new_state.get('currentTime', 0)
        
        for i in range(3):
            action = {
                "berthAllocation": i % 4,
                "agvAssignment": (i * 3) % 24,
                "yardBlockSelection": (i * 2) % 16,
                "priorityWeights": [0.3, 0.4, 0.3],
                "resourceAllocationBias": 0.5,
                "congestionAvoidance": 0.5
            }
            
            response = session.post(
                f"{base_url}/api/simulation/step",
                json=action,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            response.raise_for_status()
            
            step_result = response.json()
            new_state = step_result.get('newState', {})
            
            print(f"   Step {i+1}: Time={new_state.get('currentTime', 'N/A')}, "
                  f"Reward={step_result.get('reward', 'N/A'):.2f}, "
                  f"Delay={new_state.get('delayRate', 'N/A'):.2f}%")
        
        print("✅ Multiple steps successful")
        
    except Exception as e:
        print(f"❌ Multiple steps failed: {e}")
        return False
    
    # Summary
    print("\n" + "=" * 60)
    print("🎉 All API tests passed successfully!")
    print("✅ The .NET simulation API is working correctly")
    print("🚀 Ready for RL training!")
    
    session.close()
    return True


def test_environment_integration():
    """Test the Gym environment integration"""
    print("\n🏗️  Testing Gym Environment Integration")
    print("=" * 60)
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from port_simulation_env import PortSimulationEnv
        
        print("✅ Environment import successful")
        
        # Create environment
        env = PortSimulationEnv()
        print("✅ Environment creation successful")
        
        # Test reset
        obs, info = env.reset()
        print(f"✅ Environment reset successful")
        print(f"   Observation shape: {obs.shape}")
        print(f"   Info: {info}")
        
        # Test step
        action = {
            'discrete': [1, 5, 3],
            'continuous': [0.4, 0.3, 0.3, 0.5]
        }
        
        obs, reward, done, truncated, info = env.step(action)
        print(f"✅ Environment step successful")
        print(f"   Reward: {reward}")
        print(f"   Done: {done}")
        print(f"   Info: {info}")
        
        env.close()
        print("✅ Environment closed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment integration failed: {e}")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test WSC 2024 Port Simulation API")
    parser.add_argument("--url", type=str, default="http://localhost:5000", help="API base URL")
    parser.add_argument("--env-test", action="store_true", help="Also test Gym environment")
    
    args = parser.parse_args()
    
    # Test API connection
    api_success = test_api_connection(args.url)
    
    # Test environment if requested
    if args.env_test and api_success:
        env_success = test_environment_integration()
        
        if env_success:
            print("\n🎯 All tests passed! Ready to start RL training.")
        else:
            print("\n⚠️  API tests passed but environment integration failed.")
    elif api_success:
        print("\n🎯 API tests passed! Use --env-test to also test environment integration.")
    else:
        print("\n❌ API tests failed. Please check the .NET simulation.")
        sys.exit(1)
