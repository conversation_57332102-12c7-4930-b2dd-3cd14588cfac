"""
RL Decision Service - Provides trained RL model decisions via HTTP API
This service can be called by the .NET DecisionMaker to get RL-based decisions
"""

import os
import sys
import numpy as np
from flask import Flask, request, jsonify
from stable_baselines3 import PPO
import threading
import time

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from port_simulation_env import PortSimulationEnv

app = Flask(__name__)

class RLDecisionService:
    def __init__(self, model_path="models/ppo_port_simulation.zip"):
        self.model = None
        self.env = None
        self.current_state = None
        self.load_model(model_path)
        
    def load_model(self, model_path):
        """Load the trained RL model"""
        try:
            if os.path.exists(model_path):
                self.model = PPO.load(model_path)
                print(f"✅ RL model loaded from {model_path}")
            else:
                print(f"❌ Model file not found: {model_path}")
                print("Using random policy as fallback")
                self.model = None
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            self.model = None
    
    def update_state(self, simulation_state):
        """Update the current simulation state"""
        try:
            # Convert .NET simulation state to RL observation format
            observation = self._convert_state_to_observation(simulation_state)
            self.current_state = observation
            return True
        except Exception as e:
            print(f"Error updating state: {e}")
            return False
    
    def get_berth_decision(self, vessel_info, available_berths):
        """Get RL-based berth allocation decision"""
        if self.model is None or self.current_state is None:
            # Fallback to simple heuristic
            return self._fallback_berth_decision(available_berths)
        
        try:
            # Get RL action
            action, _ = self.model.predict(self.current_state, deterministic=True)
            
            # Convert action to berth selection
            berth_index = action[0] % len(available_berths)
            selected_berth = available_berths[berth_index]
            
            return {
                "berth_id": selected_berth,
                "confidence": 0.8,
                "method": "RL"
            }
            
        except Exception as e:
            print(f"Error in RL berth decision: {e}")
            return self._fallback_berth_decision(available_berths)
    
    def get_agv_decision(self, container_info, available_agvs):
        """Get RL-based AGV allocation decision"""
        if self.model is None or self.current_state is None:
            return self._fallback_agv_decision(available_agvs)
        
        try:
            # Get RL action
            action, _ = self.model.predict(self.current_state, deterministic=True)
            
            # Convert action to AGV selection
            agv_index = action[1] % len(available_agvs)
            selected_agv = available_agvs[agv_index]
            
            return {
                "agv_id": selected_agv,
                "confidence": 0.8,
                "method": "RL"
            }
            
        except Exception as e:
            print(f"Error in RL AGV decision: {e}")
            return self._fallback_agv_decision(available_agvs)
    
    def get_yard_decision(self, container_info, available_blocks):
        """Get RL-based yard block selection decision"""
        if self.model is None or self.current_state is None:
            return self._fallback_yard_decision(available_blocks)
        
        try:
            # Get RL action
            action, _ = self.model.predict(self.current_state, deterministic=True)
            
            # Convert action to yard block selection
            block_index = action[2] % len(available_blocks)
            selected_block = available_blocks[block_index]
            
            return {
                "yard_block_id": selected_block,
                "confidence": 0.8,
                "method": "RL"
            }
            
        except Exception as e:
            print(f"Error in RL yard decision: {e}")
            return self._fallback_yard_decision(available_blocks)
    
    def _convert_state_to_observation(self, simulation_state):
        """Convert .NET simulation state to RL observation format"""
        # This is a simplified conversion - in practice, you'd want to match
        # the exact observation format used during training

        # Extract key features
        vessels = simulation_state.get('vessels', [])
        berths = simulation_state.get('berths', [])
        agvs = simulation_state.get('agvs', [])

        # Create observation vector (simplified)
        obs = np.zeros(200)  # Match training observation size

        # Fill with basic features
        obs[0] = len(vessels)
        obs[1] = self._parse_time_to_float(simulation_state.get('currentTime', 0))
        obs[2] = simulation_state.get('delayRate', 0)

        # Add berth states
        for i, berth in enumerate(berths[:4]):
            base_idx = 10 + i * 4
            obs[base_idx] = 1 if berth.get('isOccupied', False) else 0
            obs[base_idx + 1] = berth.get('utilizationRate', 0)
            obs[base_idx + 2] = berth.get('availableQCs', 0)

        # Add vessel states (simplified)
        for i, vessel in enumerate(vessels[:20]):
            base_idx = 30 + i * 4
            obs[base_idx] = vessel.get('containerCount', 0) / 1000.0  # Normalize
            obs[base_idx + 1] = 1 if vessel.get('isDelayed', False) else 0
            obs[base_idx + 2] = self._parse_time_to_float(vessel.get('arrivalTime', 0)) / 168.0  # Normalize

        return obs

    def _parse_time_to_float(self, time_value):
        """Parse time value from .NET to float"""
        if isinstance(time_value, (int, float)):
            return float(time_value)

        if isinstance(time_value, str):
            try:
                # Try to parse .NET DateTime format: "2024-05-04T04:02:00.000001"
                from datetime import datetime
                dt = datetime.fromisoformat(time_value.replace('T', ' ').rstrip('Z'))
                # Convert to hours since epoch (simplified)
                epoch = datetime(2024, 5, 4)  # Use simulation start as epoch
                delta = dt - epoch
                return delta.total_seconds() / 3600.0  # Convert to hours
            except:
                # If parsing fails, return 0
                return 0.0

        return 0.0
    
    def _fallback_berth_decision(self, available_berths):
        """Simple fallback berth selection"""
        if available_berths:
            return {
                "berth_id": available_berths[0],
                "confidence": 0.3,
                "method": "Fallback"
            }
        return None
    
    def _fallback_agv_decision(self, available_agvs):
        """Simple fallback AGV selection"""
        if available_agvs:
            return {
                "agv_id": available_agvs[0],
                "confidence": 0.3,
                "method": "Fallback"
            }
        return None
    
    def _fallback_yard_decision(self, available_blocks):
        """Simple fallback yard block selection"""
        if available_blocks:
            return {
                "yard_block_id": available_blocks[0],
                "confidence": 0.3,
                "method": "Fallback"
            }
        return None

# Global RL service instance
rl_service = RLDecisionService()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "model_loaded": rl_service.model is not None,
        "timestamp": time.time()
    })

@app.route('/update_state', methods=['POST'])
def update_state():
    """Update the current simulation state"""
    try:
        state_data = request.json
        success = rl_service.update_state(state_data)
        return jsonify({"success": success})
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route('/berth_decision', methods=['POST'])
def berth_decision():
    """Get berth allocation decision"""
    try:
        data = request.json
        vessel_info = data.get('vessel', {})
        available_berths = data.get('available_berths', [])
        
        decision = rl_service.get_berth_decision(vessel_info, available_berths)
        return jsonify(decision)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route('/agv_decision', methods=['POST'])
def agv_decision():
    """Get AGV allocation decision"""
    try:
        data = request.json
        container_info = data.get('container', {})
        available_agvs = data.get('available_agvs', [])
        
        decision = rl_service.get_agv_decision(container_info, available_agvs)
        return jsonify(decision)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route('/yard_decision', methods=['POST'])
def yard_decision():
    """Get yard block selection decision"""
    try:
        data = request.json
        container_info = data.get('container', {})
        available_blocks = data.get('available_blocks', [])
        
        decision = rl_service.get_yard_decision(container_info, available_blocks)
        return jsonify(decision)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

def run_rl_service(port=5001):
    """Run the RL decision service"""
    print(f"🤖 Starting RL Decision Service on port {port}")
    print("🔗 This service provides RL-based decisions for the .NET simulation")
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == "__main__":
    run_rl_service()
