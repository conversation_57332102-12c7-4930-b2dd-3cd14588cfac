# WSC 2024 RL Integration Guide

## 🎯 目标
将训练好的 RL 模型集成到 .NET 仿真中，替代原有的启发式决策策略，以改善港口性能。

## 📊 当前性能基准
- **原始策略**: 延迟率 11.67% (35/300 艘船舶延迟)
- **目标**: 使用 RL 将延迟率降低到 8-10%

## 🏗️ 集成架构

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│  .NET Simulation│◄──────────────►│ Python RL Service│
│                 │                │                 │
│ • DecisionMaker │                │ • Trained Model │
│ • Port Logic    │                │ • Decision API  │
│ • Fallback      │                │ • State Updates │
└─────────────────┘                └─────────────────┘
```

## 🚀 使用步骤

### 步骤 1: 启动 RL 决策服务

```bash
# 激活虚拟环境
python_rl_env\Scripts\activate

# 启动 RL 决策服务 (端口 5001)
python python_rl\rl_decision_service.py
```

您应该看到：
```
🤖 Starting RL Decision Service on port 5001
🔗 This service provides RL-based decisions for the .NET simulation
✅ RL model loaded from models/ppo_port_simulation.zip
```

### 步骤 2: 修改 .NET 仿真使用 RL 决策器

有两种方法集成 RL：

#### 方法 A: 替换现有 DecisionMaker (推荐)

1. **备份原始文件**:
   ```bash
   copy StrategyMaking\DecisionMaker.cs StrategyMaking\DecisionMaker_Original.cs
   ```

2. **替换为 RL 版本**:
   ```bash
   copy StrategyMaking\RLDecisionMaker.cs StrategyMaking\DecisionMaker.cs
   ```

#### 方法 B: 修改 Program.cs 使用 RLDecisionMaker

在 `Program.cs` 中找到 `DecisionMaker` 的引用并替换为 `RLDecisionMaker`。

### 步骤 3: 运行集成测试

```bash
# 构建项目
dotnet build

# 运行仿真
dotnet run
```

## 📈 预期结果

### 成功集成的标志：
- ✅ 控制台显示 "🤖 RL Service: Available"
- ✅ 看到 RL 决策日志: "🤖 RL Berth Decision: berth1 (confidence: 0.80)"
- ✅ 延迟率应该低于 11.67%

### 如果 RL 服务不可用：
- ⚠️ 控制台显示 "🤖 RL Service: Unavailable (using heuristic fallback)"
- ✅ 仍然使用原始启发式策略运行
- ✅ 性能应该与原始版本相同 (11.67% 延迟率)

## 🔧 故障排除

### 问题 1: RL 服务连接失败
```
⚠️ RL Berth Decision failed: Connection refused
```

**解决方案**:
1. 确保 RL 服务正在运行 (`python python_rl\rl_decision_service.py`)
2. 检查端口 5001 是否被占用
3. 验证防火墙设置

### 问题 2: 模型文件未找到
```
❌ Model file not found: models/ppo_port_simulation.zip
```

**解决方案**:
1. 确保已训练模型: `python python_rl\train_rl_agent.py --timesteps 500`
2. 检查模型文件路径
3. 如果没有模型，RL 服务将使用随机策略

### 问题 3: .NET 编译错误

**解决方案**:
1. 确保 `RLDecisionMaker.cs` 在正确的命名空间中
2. 检查 `using` 语句
3. 重新构建项目: `dotnet clean && dotnet build`

## 📊 性能监控

### 监控指标：
- **延迟率**: 目标 < 11.67%
- **RL 决策置信度**: 应该 > 0.5
- **决策方法**: "RL" vs "Fallback"
- **响应时间**: RL 决策应该 < 2 秒

### 日志示例：
```
🤖 RL Service: Available
🤖 RL Berth Decision: berth2 (confidence: 0.85)
🤖 RL AGV Decision: agv15 (confidence: 0.78)
Number of delayed vessels: 28; Number of arrival vessels: 300
Rate of delayed vessels: 9.33 %  ← 改善！
```

## 🎯 优化建议

### 1. 模型改进
- 训练更长时间: `--timesteps 10000`
- 尝试不同算法: SAC, TD3
- 调整超参数

### 2. 状态表示改进
- 在 `_convert_state_to_observation()` 中添加更多特征
- 包含历史信息
- 标准化输入

### 3. 奖励函数优化
- 调整延迟惩罚权重
- 添加效率奖励
- 考虑多目标优化

## 🔄 回滚到原始策略

如果需要回滚到原始策略：

```bash
# 恢复原始 DecisionMaker
copy StrategyMaking\DecisionMaker_Original.cs StrategyMaking\DecisionMaker.cs

# 重新构建
dotnet build

# 运行
dotnet run
```

## 📝 下一步

1. **基准测试**: 比较 RL vs 启发式性能
2. **参数调优**: 优化 RL 模型超参数
3. **在线学习**: 实现实时模型更新
4. **多智能体**: 为不同决策类型训练专门的智能体
5. **生产部署**: 优化性能和稳定性

---

🎉 **现在您可以使用训练好的 RL 模型来改善港口仿真性能！**
