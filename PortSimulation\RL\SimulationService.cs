using WSC_SimChallenge_2024_Net.PortSimulation.Entity;
using WSC_SimChallenge_2024_Net.PortSimulation.RL;
using WSC_SimChallenge_2024_Net.StrategyMaking;

namespace WSC_SimChallenge_2024_Net.PortSimulation.RL
{
    /// <summary>
    /// Implementation of simulation service for RL integration
    /// </summary>
    public class SimulationService : ISimulationService
    {
        private PortSimModel _simulation;
        private RewardCalculator _rewardCalculator;
        private DateTime _lastStepTime;
        private SimulationState _lastState;

        public SimulationService()
        {
            _rewardCalculator = new RewardCalculator();
            InitializeSimulation();
        }

        public SimulationState Reset()
        {
            // Initialize new simulation instance
            InitializeSimulation();
            
            // Get initial state
            _lastState = GetCurrentState();
            _lastStepTime = _simulation.ClockTime;
            
            return _lastState;
        }

        public StepResult Step(PortAction action)
        {
            var previousState = _lastState;
            
            // Apply action to simulation
            ApplyAction(action);
            
            // Advance simulation by one time step (e.g., 1 hour)
            var stepDuration = TimeSpan.FromHours(1);
            _simulation.Run(stepDuration);
            
            // Get new state
            var newState = GetCurrentState();
            
            // Calculate reward
            var reward = RewardCalculator.CalculateReward(previousState, action, newState);
            
            // Check if simulation is done (e.g., end of week)
            var done = _simulation.ClockTime >= _simulation.StartTime.AddDays(7);
            
            _lastState = newState;
            _lastStepTime = _simulation.ClockTime;
            
            return new StepResult
            {
                NewState = newState,
                Reward = reward,
                Done = done,
                Info = new Dictionary<string, object>
                {
                    ["simulation_time"] = _simulation.ClockTime,
                    ["step_duration"] = stepDuration.TotalHours,
                    ["vessels_processed"] = newState.Vessels.Length,
                    ["delay_improvement"] = previousState?.DelayRate - newState.DelayRate ?? 0
                }
            };
        }

        public SimulationState GetCurrentState()
        {
            if (_simulation == null)
            {
                InitializeSimulation();
            }

            // Extract current state from simulation
            var vessels = ExtractVesselStates();
            var berths = ExtractBerthStates();
            var agvs = ExtractAGVStates();
            var qcs = ExtractQCStates();
            var yardBlocks = ExtractYardBlockStates();

            // Calculate performance metrics
            var totalVessels = vessels.Length;
            var delayedVessels = vessels.Count(v => v.IsDelayed);
            var delayRate = totalVessels > 0 ? (double)delayedVessels / totalVessels * 100 : 0;

            return new SimulationState
            {
                Vessels = vessels,
                Berths = berths,
                AGVs = agvs,
                QCs = qcs,
                YardBlocks = yardBlocks,
                CurrentTime = _simulation.ClockTime.Subtract(_simulation.StartTime).TotalHours,
                TotalDelayedVessels = delayedVessels,
                DelayRate = delayRate
            };
        }

        public PerformanceMetrics GetPerformanceMetrics()
        {
            var state = GetCurrentState();
            
            return new PerformanceMetrics
            {
                VesselDelayRate = state.DelayRate,
                TotalDelayedVessels = state.TotalDelayedVessels,
                TotalVessels = state.Vessels.Length,
                AverageWaitingTime = state.Vessels.Average(v => v.WaitingTime),
                ThroughputRate = CalculateThroughputRate(),
                ResourceUtilization = CalculateResourceUtilization(state),
                LastUpdated = DateTime.Now
            };
        }

        private void InitializeSimulation()
        {
            _simulation = new PortSimModel()
            {
                NumberofAGVs = 24, // As per RL plan
                StartTime = new DateTime(2024, 5, 4, 0, 0, 0)
            };
            _simulation.Initialize();
            DecisionMaker.WSCPort = _simulation;
        }

        private void ApplyAction(PortAction action)
        {
            // Store action for decision maker to use
            // This will be used by the enhanced decision maker
            RLActionContext.CurrentAction = action;
        }

        private VesselState[] ExtractVesselStates()
        {
            var vessels = new List<VesselState>();

            // Extract from all vessels in simulation
            foreach (var vessel in _simulation.Vessels)
            {
                if (vessel.ArrivalTime != DateTime.MinValue)
                {
                    var totalContainers = vessel.DischargingContainersInformation.Values.Sum() +
                                        vessel.LoadingContainersInformation.Values.Sum();
                    var isDelayed = vessel.ArrivalTime != DateTime.MinValue &&
                                  vessel.StartBerthingTime != DateTime.MinValue &&
                                  vessel.StartBerthingTime.Subtract(vessel.ArrivalTime) > TimeSpan.FromHours(2);

                    vessels.Add(new VesselState
                    {
                        Id = vessel.Id,
                        ArrivalTime = vessel.ArrivalTime.Subtract(_simulation.StartTime).TotalHours,
                        ContainerCount = totalContainers,
                        IsDelayed = isDelayed,
                        WaitingTime = _simulation.ClockTime.Subtract(vessel.ArrivalTime).TotalHours,
                        AssignedBerth = vessel.AllocatedBerth != null ? int.Parse(vessel.AllocatedBerth.Id.Replace("berth", "")) : -1
                    });
                }
            }

            return vessels.ToArray();
        }

        private BerthState[] ExtractBerthStates()
        {
            return _simulation.Berths.Select(berth => new BerthState
            {
                Id = berth.Id,
                IsOccupied = berth.BerthedVessel != null,
                OccupiedByVessel = berth.BerthedVessel?.Id ?? "",
                UtilizationRate = CalculateBerthUtilization(berth),
                AvailableQCs = berth.EquippedQCs.Count(qc => qc.ServedVessel == null)
            }).ToArray();
        }

        private AGVState[] ExtractAGVStates()
        {
            return _simulation.AGVs.Select(agv => new AGVState
            {
                Id = agv.Id,
                IsIdle = agv.LoadedContainer == null,
                HasContainer = agv.LoadedContainer != null,
                Position = new double[] { agv.CurrentLocation.Xcoordinate, agv.CurrentLocation.Ycoordinate },
                DistanceToTarget = CalculateDistanceToTarget(agv)
            }).ToArray();
        }

        private QCState[] ExtractQCStates()
        {
            return _simulation.QCs.Select(qc => new QCState
            {
                Id = qc.Id,
                IsWorking = qc.ServedVessel != null,
                AssignedBerth = qc.LocatedBerth?.Id ?? "",
                ProductivityRate = 30.0 // Default productivity rate (containers/hour)
            }).ToArray();
        }

        private YardBlockState[] ExtractYardBlockStates()
        {
            return _simulation.YardBlocks.Select(yb => new YardBlockState
            {
                Id = yb.Id,
                Capacity = yb.Capacity,
                CurrentLoad = yb.StackedContainers.Count,
                UtilizationRate = (double)yb.StackedContainers.Count / yb.Capacity * 100,
                ReservedSlots = yb.ReservedSlots
            }).ToArray();
        }

        private double CalculateBerthUtilization(Berth berth)
        {
            // Simplified calculation - in real implementation, track historical data
            return berth.BerthedVessel != null ? 100.0 : 0.0;
        }

        private double CalculateDistanceToTarget(AGV agv)
        {
            if (agv.TargetedQC != null)
                return Math.Sqrt(Math.Pow(agv.CurrentLocation.Xcoordinate - agv.TargetedQC.CP.Xcoordinate, 2) +
                               Math.Pow(agv.CurrentLocation.Ycoordinate - agv.TargetedQC.CP.Ycoordinate, 2));
            if (agv.TargetedYB != null)
                return Math.Sqrt(Math.Pow(agv.CurrentLocation.Xcoordinate - agv.TargetedYB.CP.Xcoordinate, 2) +
                               Math.Pow(agv.CurrentLocation.Ycoordinate - agv.TargetedYB.CP.Ycoordinate, 2));
            return 0.0;
        }

        private double CalculateThroughputRate()
        {
            // Simplified calculation - containers processed per hour
            // In real implementation, track container movements
            return 50.0; // Placeholder
        }

        private double CalculateResourceUtilization(SimulationState state)
        {
            var berthUtil = state.Berths.Average(b => b.UtilizationRate);
            var agvUtil = state.AGVs.Count(a => !a.IsIdle) / (double)state.AGVs.Length * 100;
            var yardUtil = state.YardBlocks.Average(y => y.UtilizationRate);
            
            return (berthUtil + agvUtil + yardUtil) / 3.0;
        }
    }

    /// <summary>
    /// Context for storing current RL action for decision makers
    /// </summary>
    public static class RLActionContext
    {
        public static PortAction? CurrentAction { get; set; }
    }
}
