"""
Training script for WSC 2024 Port Simulation RL Agent
Uses Stable-Baselines3 with PPO algorithm
"""

import os
import sys
import time
import numpy as np
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.callbacks import Eval<PERSON><PERSON>back, StopTrainingOnRewardThreshold
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
import gymnasium as gym

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from port_simulation_env import PortSimulationEnv


def create_env(api_url="http://localhost:5000"):
    """Create and wrap the port simulation environment"""
    env = PortSimulationEnv(simulation_endpoint=api_url, max_steps=168)  # 1 week
    env = Monitor(env)
    return env


def train_agent(
    total_timesteps=50000,
    api_url="http://localhost:5000",
    model_save_path="models/ppo_port_simulation",
    log_dir="logs/"
):
    """Train the RL agent using PPO"""
    
    print("🚀 Starting WSC 2024 Port Simulation RL Training")
    print(f"📡 API URL: {api_url}")
    print(f"⏱️  Total timesteps: {total_timesteps}")
    print(f"💾 Model save path: {model_save_path}")
    
    # Create directories
    os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Test API connection first
    print("\n🔍 Testing API connection...")
    try:
        import requests
        response = requests.get(f"{api_url}/api/simulation/state", timeout=5)
        if response.status_code == 200:
            print("✅ API connection successful")
        else:
            print(f"⚠️  API returned status {response.status_code}")
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        print("Please ensure the .NET simulation is running with --api flag")
        return None
    
    # Create environment
    print("\n🏗️  Creating environment...")
    try:
        env = create_env(api_url)
        print("✅ Environment created successfully")
        
        # Test environment
        print("🧪 Testing environment reset...")
        obs, info = env.reset()
        print(f"✅ Environment reset successful. Observation shape: {obs.shape}")
        
    except Exception as e:
        print(f"❌ Environment creation failed: {e}")
        return None
    
    # Create PPO model
    print("\n🤖 Creating PPO model...")
    model = PPO(
        "MlpPolicy",
        env,
        verbose=1,
        tensorboard_log=log_dir,
        learning_rate=3e-4,
        n_steps=2048,
        batch_size=64,
        n_epochs=10,
        gamma=0.99,
        gae_lambda=0.95,
        clip_range=0.2,
        ent_coef=0.01,
        vf_coef=0.5,
        max_grad_norm=0.5,
        device="auto"
    )
    
    print("✅ PPO model created")
    print(f"📊 Policy: {model.policy}")
    print(f"🎯 Action space: {env.action_space}")
    print(f"👁️  Observation space: {env.observation_space}")
    
    # Setup callbacks
    print("\n⚙️  Setting up callbacks...")

    # Create evaluation environment
    eval_env = create_env(api_url)

    # Stop training when reward threshold is reached (must be used with EvalCallback)
    reward_threshold = -5.0  # Target: delay rate < 5%
    stop_callback = StopTrainingOnRewardThreshold(
        reward_threshold=reward_threshold,
        verbose=1
    )

    # Combine callbacks - stop_callback will be triggered by eval_callback
    eval_callback = EvalCallback(
        eval_env,
        best_model_save_path=f"{model_save_path}_best",
        log_path=log_dir,
        eval_freq=5000,
        deterministic=True,
        render=False,
        n_eval_episodes=3,
        callback_after_eval=stop_callback  # This is the correct way
    )

    callbacks = [eval_callback]
    
    # Start training
    print(f"\n🎯 Starting training for {total_timesteps} timesteps...")
    print(f"🏆 Target reward threshold: {reward_threshold}")
    print("📈 Training progress will be logged to TensorBoard")
    print(f"💡 Run: tensorboard --logdir {log_dir}")
    
    start_time = time.time()
    
    try:
        model.learn(
            total_timesteps=total_timesteps,
            callback=callbacks,
            tb_log_name="ppo_port_simulation",
            progress_bar=True
        )
        
        training_time = time.time() - start_time
        print(f"\n✅ Training completed in {training_time:.2f} seconds")
        
        # Save final model
        model.save(model_save_path)
        print(f"💾 Model saved to {model_save_path}")
        
        # Test trained model
        print("\n🧪 Testing trained model...")
        test_model(model, env, episodes=3)
        
        return model
        
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
        model.save(f"{model_save_path}_interrupted")
        print(f"💾 Partial model saved to {model_save_path}_interrupted")
        return model
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        return None
    
    finally:
        env.close()
        eval_env.close()


def test_model(model, env, episodes=5):
    """Test the trained model"""
    print(f"\n🎮 Testing model for {episodes} episodes...")
    
    total_rewards = []
    total_delay_rates = []
    
    for episode in range(episodes):
        obs, info = env.reset()
        episode_reward = 0
        episode_steps = 0
        
        print(f"\n📊 Episode {episode + 1}/{episodes}")
        
        while True:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, done, truncated, info = env.step(action)
            
            episode_reward += reward
            episode_steps += 1
            
            if episode_steps % 24 == 0:  # Print every 24 hours
                delay_rate = info.get('delay_rate', 0)
                sim_time = info.get('simulation_time', 0)
                print(f"  Hour {sim_time:.1f}: Delay Rate = {delay_rate:.2f}%, Reward = {reward:.2f}")
            
            if done or truncated:
                break
        
        final_delay_rate = info.get('delay_rate', 0)
        total_rewards.append(episode_reward)
        total_delay_rates.append(final_delay_rate)
        
        print(f"  ✅ Episode {episode + 1} completed:")
        print(f"     Total Reward: {episode_reward:.2f}")
        print(f"     Final Delay Rate: {final_delay_rate:.2f}%")
        print(f"     Steps: {episode_steps}")
    
    # Summary
    avg_reward = np.mean(total_rewards)
    avg_delay_rate = np.mean(total_delay_rates)
    
    print(f"\n📈 Test Results Summary:")
    print(f"   Average Reward: {avg_reward:.2f} ± {np.std(total_rewards):.2f}")
    print(f"   Average Delay Rate: {avg_delay_rate:.2f}% ± {np.std(total_delay_rates):.2f}%")
    print(f"   Best Delay Rate: {min(total_delay_rates):.2f}%")
    print(f"   Worst Delay Rate: {max(total_delay_rates):.2f}%")


def load_and_test(model_path, api_url="http://localhost:5000", episodes=5):
    """Load a trained model and test it"""
    print(f"📂 Loading model from {model_path}")
    
    try:
        model = PPO.load(model_path)
        env = create_env(api_url)
        
        print("✅ Model loaded successfully")
        test_model(model, env, episodes)
        
        env.close()
        
    except Exception as e:
        print(f"❌ Failed to load and test model: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Train WSC 2024 Port Simulation RL Agent")
    parser.add_argument("--timesteps", type=int, default=50000, help="Total training timesteps")
    parser.add_argument("--api-url", type=str, default="http://localhost:5000", help="Simulation API URL")
    parser.add_argument("--model-path", type=str, default="models/ppo_port_simulation", help="Model save path")
    parser.add_argument("--test-only", type=str, help="Path to model to test (skip training)")
    parser.add_argument("--episodes", type=int, default=5, help="Number of test episodes")
    
    args = parser.parse_args()
    
    if args.test_only:
        load_and_test(args.test_only, args.api_url, args.episodes)
    else:
        train_agent(
            total_timesteps=args.timesteps,
            api_url=args.api_url,
            model_save_path=args.model_path
        )
